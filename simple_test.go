package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	db, err := sql.Open("mysql", "root:123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 测试知识搜索功能...")

	// 1. 查看所有学习知识
	fmt.Println("\n📚 所有学习知识:")
	query := `SELECT id, question, LEFT(answer, 50) as answer_preview FROM learned_knowledge ORDER BY created_at DESC LIMIT 5`
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var question, answerPreview string
		rows.Scan(&id, &question, &answerPreview)
		fmt.Printf("  ID %d: %s -> %s\n", id, question, answerPreview)
	}

	// 2. 添加测试知识
	fmt.Println("\n➕ 添加测试知识...")
	insertQuery := `
		INSERT INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	_, err = db.Exec(insertQuery, 
		"什么是一言百宝箱？", 
		"一言百宝箱是一个提供随机名言警句的在线服务。", 
		"manual_test", 0.9, "test", 
		`["一言百宝箱", "名言"]`, 
		"测试数据", "test_user", "approved")
	if err != nil {
		fmt.Printf("❌ 添加失败: %v\n", err)
	} else {
		fmt.Println("✅ 添加成功")
	}

	// 3. 测试搜索
	fmt.Println("\n🔍 搜索 '一言百宝箱':")
	searchQuery := `
		SELECT id, question, answer
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		AND (question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%'))
		LIMIT 5
	`
	
	searchRows, err := db.Query(searchQuery, "一言百宝箱", "一言百宝箱")
	if err != nil {
		fmt.Printf("❌ 搜索失败: %v\n", err)
		return
	}
	defer searchRows.Close()

	found := false
	for searchRows.Next() {
		var id int
		var question, answer string
		searchRows.Scan(&id, &question, &answer)
		
		if !found {
			fmt.Println("找到的结果:")
			found = true
		}
		
		fmt.Printf("  ID %d: %s -> %s\n", id, question, answer)
	}
	
	if !found {
		fmt.Println("❌ 未找到匹配结果")
	}
}
