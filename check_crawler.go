package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/database/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	fmt.Println("🕷️ 检查爬虫系统状态...")

	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 检查爬取目标
	fmt.Println("\n📋 检查爬取目标...")
	checkCrawlTargets(db)

	// 检查爬取结果
	fmt.Println("\n📊 检查爬取结果...")
	checkCrawlResults(db)

	// 检查爬取日志
	fmt.Println("\n📝 检查爬取日志...")
	checkCrawlLogs(db)
}

func checkCrawlTargets(db *sql.DB) {
	// 检查表是否存在
	var tableExists int
	err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'crawl_targets'").Scan(&tableExists)
	if err != nil {
		fmt.Printf("❌ 检查crawl_targets表失败: %v\n", err)
		return
	}

	if tableExists == 0 {
		fmt.Println("❌ crawl_targets表不存在")
		return
	}

	fmt.Println("✅ crawl_targets表存在")

	// 查询爬取目标数量
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM crawl_targets").Scan(&count)
	if err != nil {
		fmt.Printf("❌ 查询爬取目标数量失败: %v\n", err)
		return
	}

	fmt.Printf("📊 爬取目标总数: %d\n", count)

	if count == 0 {
		fmt.Println("⚠️  没有配置任何爬取目标")
		return
	}

	// 显示所有爬取目标
	fmt.Println("\n📋 爬取目标列表:")
	rows, err := db.Query(`
		SELECT id, name, url, type, category, keywords, enabled, last_crawled, created_at
		FROM crawl_targets 
		ORDER BY id
	`)
	if err != nil {
		fmt.Printf("❌ 查询爬取目标失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("ID\t名称\t\t\tURL\t\t\t类型\t分类\t关键词\t\t启用\t最后爬取")
	fmt.Println("---\t---\t\t\t---\t\t\t---\t---\t---\t\t---\t---")

	for rows.Next() {
		var id int
		var name, url, targetType, category, keywordsJSON string
		var enabled bool
		var lastCrawled, createdAt sql.NullTime

		err := rows.Scan(&id, &name, &url, &targetType, &category, &keywordsJSON, &enabled, &lastCrawled, &createdAt)
		if err != nil {
			fmt.Printf("❌ 扫描行失败: %v\n", err)
			continue
		}

		// 解析关键词
		var keywords []string
		if keywordsJSON != "" {
			json.Unmarshal([]byte(keywordsJSON), &keywords)
		}

		// 截断长文本
		if len(name) > 15 {
			name = name[:15] + "..."
		}
		if len(url) > 30 {
			url = url[:30] + "..."
		}

		keywordsStr := fmt.Sprintf("%v", keywords)
		if len(keywordsStr) > 20 {
			keywordsStr = keywordsStr[:20] + "..."
		}

		enabledStr := "否"
		if enabled {
			enabledStr = "是"
		}

		lastCrawledStr := "从未"
		if lastCrawled.Valid {
			lastCrawledStr = lastCrawled.Time.Format("01-02 15:04")
		}

		fmt.Printf("%d\t%s\t%s\t%s\t%s\t%s\t%s\t%s\n",
			id, name, url, targetType, category, keywordsStr, enabledStr, lastCrawledStr)
	}
}

func checkCrawlResults(db *sql.DB) {
	// 检查表是否存在
	var tableExists int
	err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'crawl_results'").Scan(&tableExists)
	if err != nil {
		fmt.Printf("❌ 检查crawl_results表失败: %v\n", err)
		return
	}

	if tableExists == 0 {
		fmt.Println("❌ crawl_results表不存在")
		return
	}

	fmt.Println("✅ crawl_results表存在")

	// 查询爬取结果数量
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM crawl_results").Scan(&count)
	if err != nil {
		fmt.Printf("❌ 查询爬取结果数量失败: %v\n", err)
		return
	}

	fmt.Printf("📊 爬取结果总数: %d\n", count)

	if count == 0 {
		fmt.Println("⚠️  没有任何爬取结果")
		return
	}

	// 按状态统计
	fmt.Println("\n📊 按状态统计:")
	statusRows, err := db.Query("SELECT status, COUNT(*) FROM crawl_results GROUP BY status")
	if err != nil {
		fmt.Printf("❌ 查询状态统计失败: %v\n", err)
		return
	}
	defer statusRows.Close()

	for statusRows.Next() {
		var status string
		var count int
		err := statusRows.Scan(&status, &count)
		if err != nil {
			continue
		}
		fmt.Printf("  %s: %d\n", status, count)
	}

	// 显示最近的爬取结果
	fmt.Println("\n📋 最近的爬取结果:")
	rows, err := db.Query(`
		SELECT r.id, r.target_id, t.name, r.title, r.status, r.crawled_at
		FROM crawl_results r
		LEFT JOIN crawl_targets t ON r.target_id = t.id
		ORDER BY r.crawled_at DESC
		LIMIT 10
	`)
	if err != nil {
		fmt.Printf("❌ 查询爬取结果失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("ID\t目标ID\t目标名称\t\t标题\t\t\t状态\t爬取时间")
	fmt.Println("---\t---\t---\t\t\t---\t\t\t---\t---")

	for rows.Next() {
		var id, targetID int
		var targetName, title, status string
		var crawledAt sql.NullTime

		err := rows.Scan(&id, &targetID, &targetName, &title, &status, &crawledAt)
		if err != nil {
			fmt.Printf("❌ 扫描行失败: %v\n", err)
			continue
		}

		// 截断长文本
		if len(targetName) > 15 {
			targetName = targetName[:15] + "..."
		}
		if len(title) > 20 {
			title = title[:20] + "..."
		}

		crawledAtStr := "未知"
		if crawledAt.Valid {
			crawledAtStr = crawledAt.Time.Format("01-02 15:04")
		}

		fmt.Printf("%d\t%d\t%s\t%s\t%s\t%s\n",
			id, targetID, targetName, title, status, crawledAtStr)
	}
}

func checkCrawlLogs(db *sql.DB) {
	// 检查表是否存在
	var tableExists int
	err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'crawl_logs'").Scan(&tableExists)
	if err != nil {
		fmt.Printf("❌ 检查crawl_logs表失败: %v\n", err)
		return
	}

	if tableExists == 0 {
		fmt.Println("❌ crawl_logs表不存在")
		return
	}

	fmt.Println("✅ crawl_logs表存在")

	// 查询日志数量
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM crawl_logs").Scan(&count)
	if err != nil {
		fmt.Printf("❌ 查询爬取日志数量失败: %v\n", err)
		return
	}

	fmt.Printf("📊 爬取日志总数: %d\n", count)

	if count == 0 {
		fmt.Println("⚠️  没有任何爬取日志")
		return
	}

	// 按状态统计
	fmt.Println("\n📊 按状态统计:")
	statusRows, err := db.Query("SELECT status, COUNT(*) FROM crawl_logs GROUP BY status")
	if err != nil {
		fmt.Printf("❌ 查询状态统计失败: %v\n", err)
		return
	}
	defer statusRows.Close()

	for statusRows.Next() {
		var status string
		var count int
		err := statusRows.Scan(&status, &count)
		if err != nil {
			continue
		}
		fmt.Printf("  %s: %d\n", status, count)
	}

	// 显示最近的错误日志
	fmt.Println("\n📋 最近的错误日志:")
	rows, err := db.Query(`
		SELECT l.id, l.target_id, t.name, l.status, l.message, l.created_at
		FROM crawl_logs l
		LEFT JOIN crawl_targets t ON l.target_id = t.id
		WHERE l.status != 'success'
		ORDER BY l.created_at DESC
		LIMIT 5
	`)
	if err != nil {
		fmt.Printf("❌ 查询错误日志失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("ID\t目标ID\t目标名称\t\t状态\t错误信息\t\t\t时间")
	fmt.Println("---\t---\t---\t\t\t---\t---\t\t\t\t---")

	for rows.Next() {
		var id, targetID int
		var targetName, status, message string
		var createdAt sql.NullTime

		err := rows.Scan(&id, &targetID, &targetName, &status, &message, &createdAt)
		if err != nil {
			fmt.Printf("❌ 扫描行失败: %v\n", err)
			continue
		}

		// 截断长文本
		if len(targetName) > 15 {
			targetName = targetName[:15] + "..."
		}
		if len(message) > 30 {
			message = message[:30] + "..."
		}

		createdAtStr := "未知"
		if createdAt.Valid {
			createdAtStr = createdAt.Time.Format("01-02 15:04")
		}

		fmt.Printf("%d\t%d\t%s\t%s\t%s\t%s\n",
			id, targetID, targetName, status, message, createdAtStr)
	}
}
