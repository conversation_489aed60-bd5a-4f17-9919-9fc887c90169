package main

import (
	"fmt"
	"faq-system/internal/learning"
)

func main() {
	// 创建一个测试知识
	knowledge := &learning.LearnedKnowledge{
		Question: "测试问题",
		Answer:   "测试答案",
		Source:   "test",
	}

	// 创建KnowledgeLearner实例（使用nil参数进行测试）
	learner := learning.NewKnowledgeLearner(nil, nil, nil)

	// 测试SaveKnowledge方法是否存在
	fmt.Printf("KnowledgeLearner类型: %T\n", learner)
	fmt.Printf("测试知识: %+v\n", knowledge)

	// 尝试调用SaveKnowledge方法
	err := learner.SaveKnowledge(knowledge)
	if err != nil {
		fmt.Printf("SaveKnowledge方法存在，但执行失败（预期的，因为数据库为nil）: %v\n", err)
	} else {
		fmt.Printf("SaveKnowledge方法存在且执行成功\n")
	}
}
