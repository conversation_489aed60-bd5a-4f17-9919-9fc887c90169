package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 诊断知识处理流程...")

	// 1. 检查爬取结果
	fmt.Println("\n📊 1. 检查爬取结果:")
	checkCrawlResults(db)

	// 2. 检查学习知识
	fmt.Println("\n📚 2. 检查学习知识:")
	checkLearnedKnowledge(db)

	// 3. 检查知识向量
	fmt.Println("\n🔢 3. 检查知识向量:")
	checkKnowledgeVectors(db)

	// 4. 搜索测试
	fmt.Println("\n🔍 4. 搜索测试:")
	testKnowledgeSearch(db, "一言百宝箱")

	// 5. 检查处理状态
	fmt.Println("\n⚙️ 5. 检查处理状态:")
	checkProcessingStatus(db)
}

func checkCrawlResults(db *sql.DB) {
	// 查询爬取结果
	query := `
		SELECT id, target_id, title, status, LENGTH(content) as content_len, 
		       crawled_at, processed_at
		FROM crawl_results 
		ORDER BY crawled_at DESC 
		LIMIT 10
	`
	
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询爬取结果失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("ID\t目标ID\t标题\t\t\t状态\t内容长度\t爬取时间\t\t处理时间")
	fmt.Println("---\t---\t---\t\t\t---\t---\t\t---\t\t\t---")

	count := 0
	for rows.Next() {
		var id, targetID, contentLen int
		var title, status, crawledAt string
		var processedAt sql.NullString

		err := rows.Scan(&id, &targetID, &title, &status, &contentLen, &crawledAt, &processedAt)
		if err != nil {
			continue
		}

		// 截断长标题
		if len(title) > 20 {
			title = title[:20] + "..."
		}

		processedAtStr := "未处理"
		if processedAt.Valid {
			processedAtStr = processedAt.String[:16]
		}

		fmt.Printf("%d\t%d\t%s\t%s\t%d\t\t%s\t%s\n",
			id, targetID, title, status, contentLen, crawledAt[:16], processedAtStr)
		count++
	}

	if count == 0 {
		fmt.Println("⚠️  没有找到任何爬取结果")
	} else {
		fmt.Printf("✅ 找到 %d 个爬取结果\n", count)
	}

	// 检查是否有包含"一言百宝箱"的内容
	contentQuery := `
		SELECT id, title, LEFT(content, 200) as content_preview
		FROM crawl_results 
		WHERE content LIKE '%一言百宝箱%'
		LIMIT 5
	`
	
	contentRows, err := db.Query(contentQuery)
	if err == nil {
		defer contentRows.Close()
		
		hasContent := false
		for contentRows.Next() {
			var id int
			var title, contentPreview string
			contentRows.Scan(&id, &title, &contentPreview)
			
			if !hasContent {
				fmt.Println("\n📝 包含'一言百宝箱'的爬取结果:")
				hasContent = true
			}
			
			fmt.Printf("  ID %d: %s\n", id, title)
			fmt.Printf("  内容预览: %s...\n", contentPreview)
		}
		
		if !hasContent {
			fmt.Println("\n⚠️  没有找到包含'一言百宝箱'的爬取结果")
		}
	}
}

func checkLearnedKnowledge(db *sql.DB) {
	// 查询学习知识
	query := `
		SELECT id, question, answer, source, confidence, status, created_at
		FROM learned_knowledge 
		ORDER BY created_at DESC 
		LIMIT 10
	`
	
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询学习知识失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("ID\t问题\t\t\t答案\t\t\t来源\t置信度\t状态\t创建时间")
	fmt.Println("---\t---\t\t\t---\t\t\t---\t---\t---\t---")

	count := 0
	for rows.Next() {
		var id int
		var question, answer, source, status, createdAt string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &source, &confidence, &status, &createdAt)
		if err != nil {
			continue
		}

		// 截断长文本
		if len(question) > 20 {
			question = question[:20] + "..."
		}
		if len(answer) > 20 {
			answer = answer[:20] + "..."
		}

		fmt.Printf("%d\t%s\t%s\t%s\t%.2f\t%s\t%s\n",
			id, question, answer, source, confidence, status, createdAt[:16])
		count++
	}

	if count == 0 {
		fmt.Println("⚠️  没有找到任何学习知识")
	} else {
		fmt.Printf("✅ 找到 %d 个学习知识\n", count)
	}

	// 检查是否有包含"一言百宝箱"的知识
	knowledgeQuery := `
		SELECT id, question, answer
		FROM learned_knowledge 
		WHERE question LIKE '%一言百宝箱%' OR answer LIKE '%一言百宝箱%'
		LIMIT 5
	`
	
	knowledgeRows, err := db.Query(knowledgeQuery)
	if err == nil {
		defer knowledgeRows.Close()
		
		hasKnowledge := false
		for knowledgeRows.Next() {
			var id int
			var question, answer string
			knowledgeRows.Scan(&id, &question, &answer)
			
			if !hasKnowledge {
				fmt.Println("\n📚 包含'一言百宝箱'的学习知识:")
				hasKnowledge = true
			}
			
			fmt.Printf("  ID %d: %s -> %s\n", id, question, answer)
		}
		
		if !hasKnowledge {
			fmt.Println("\n⚠️  没有找到包含'一言百宝箱'的学习知识")
		}
	}
}

func checkKnowledgeVectors(db *sql.DB) {
	// 查询知识向量
	query := `
		SELECT kv.id, kv.knowledge_id, kv.vector_type, 
		       LENGTH(kv.vector_data) as vector_size,
		       lk.question
		FROM knowledge_vectors kv
		LEFT JOIN learned_knowledge lk ON kv.knowledge_id = lk.id
		ORDER BY kv.id DESC 
		LIMIT 10
	`
	
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询知识向量失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("向量ID\t知识ID\t类型\t\t向量大小\t问题")
	fmt.Println("---\t---\t---\t\t---\t\t---")

	count := 0
	for rows.Next() {
		var vectorID, knowledgeID, vectorSize int
		var vectorType, question string

		err := rows.Scan(&vectorID, &knowledgeID, &vectorType, &vectorSize, &question)
		if err != nil {
			continue
		}

		// 截断长问题
		if len(question) > 30 {
			question = question[:30] + "..."
		}

		fmt.Printf("%d\t%d\t%s\t%d bytes\t%s\n",
			vectorID, knowledgeID, vectorType, vectorSize, question)
		count++
	}

	if count == 0 {
		fmt.Println("⚠️  没有找到任何知识向量")
	} else {
		fmt.Printf("✅ 找到 %d 个知识向量\n", count)
	}
}

func testKnowledgeSearch(db *sql.DB, searchTerm string) {
	fmt.Printf("搜索词: '%s'\n", searchTerm)

	// 1. 文本搜索测试
	fmt.Println("\n📝 文本搜索测试:")
	textQuery := `
		SELECT id, question, answer, confidence
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		AND (question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%'))
		ORDER BY confidence DESC
		LIMIT 5
	`
	
	rows, err := db.Query(textQuery, searchTerm, searchTerm)
	if err != nil {
		fmt.Printf("❌ 文本搜索失败: %v\n", err)
	} else {
		defer rows.Close()
		
		found := false
		for rows.Next() {
			var id int
			var question, answer string
			var confidence float32
			
			rows.Scan(&id, &question, &answer, &confidence)
			
			if !found {
				fmt.Println("找到的结果:")
				found = true
			}
			
			fmt.Printf("  ID %d (置信度 %.2f): %s -> %s\n", id, confidence, question, answer)
		}
		
		if !found {
			fmt.Println("❌ 文本搜索未找到结果")
		}
	}

	// 2. 模糊搜索测试
	fmt.Println("\n🔍 模糊搜索测试:")
	fuzzyQuery := `
		SELECT id, question, answer, confidence
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC
		LIMIT 20
	`
	
	fuzzyRows, err := db.Query(fuzzyQuery)
	if err != nil {
		fmt.Printf("❌ 模糊搜索失败: %v\n", err)
	} else {
		defer fuzzyRows.Close()
		
		found := false
		searchLower := strings.ToLower(searchTerm)
		
		for fuzzyRows.Next() {
			var id int
			var question, answer string
			var confidence float32
			
			fuzzyRows.Scan(&id, &question, &answer, &confidence)
			
			questionLower := strings.ToLower(question)
			answerLower := strings.ToLower(answer)
			
			if strings.Contains(questionLower, searchLower) || strings.Contains(answerLower, searchLower) {
				if !found {
					fmt.Println("找到的结果:")
					found = true
				}
				
				fmt.Printf("  ID %d (置信度 %.2f): %s -> %s\n", id, confidence, question, answer)
			}
		}
		
		if !found {
			fmt.Println("❌ 模糊搜索未找到结果")
		}
	}
}

func checkProcessingStatus(db *sql.DB) {
	// 检查处理状态统计
	fmt.Println("爬取结果处理状态:")
	statusQuery := `
		SELECT status, COUNT(*) as count
		FROM crawl_results
		GROUP BY status
	`
	
	rows, err := db.Query(statusQuery)
	if err != nil {
		fmt.Printf("❌ 查询处理状态失败: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var status string
		var count int
		rows.Scan(&status, &count)
		fmt.Printf("  %s: %d\n", status, count)
	}

	// 检查学习知识状态统计
	fmt.Println("\n学习知识状态:")
	knowledgeStatusQuery := `
		SELECT status, COUNT(*) as count
		FROM learned_knowledge
		GROUP BY status
	`
	
	knowledgeRows, err := db.Query(knowledgeStatusQuery)
	if err != nil {
		fmt.Printf("❌ 查询知识状态失败: %v\n", err)
		return
	}
	defer knowledgeRows.Close()

	for knowledgeRows.Next() {
		var status string
		var count int
		knowledgeRows.Scan(&status, &count)
		fmt.Printf("  %s: %d\n", status, count)
	}
}
