package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 测试知识搜索功能...")

	// 1. 查看所有学习知识
	fmt.Println("\n📚 所有学习知识:")
	showAllKnowledge(db)

	// 2. 测试搜索功能
	searchTerms := []string{"一言百宝箱", "微信", "百度", "热搜", "success"}
	
	for _, term := range searchTerms {
		fmt.Printf("\n🔍 搜索: '%s'\n", term)
		testSearch(db, term)
	}

	// 3. 手动添加一个包含"一言百宝箱"的测试知识
	fmt.Println("\n➕ 添加测试知识...")
	addTestKnowledge(db)

	// 4. 再次测试搜索
	fmt.Println("\n🔍 再次搜索 '一言百宝箱':")
	testSearch(db, "一言百宝箱")
}

func showAllKnowledge(db *sql.DB) {
	query := `
		SELECT id, question, LEFT(answer, 50) as answer_preview, status, confidence
		FROM learned_knowledge 
		ORDER BY created_at DESC 
		LIMIT 10
	`
	
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("ID\t问题\t\t\t\t答案预览\t\t\t\t状态\t置信度")
	fmt.Println("---\t---\t\t\t\t---\t\t\t\t\t---\t---")

	count := 0
	for rows.Next() {
		var id int
		var question, answerPreview, status string
		var confidence float32

		err := rows.Scan(&id, &question, &answerPreview, &status, &confidence)
		if err != nil {
			continue
		}

		// 截断长文本
		if len(question) > 30 {
			question = question[:30] + "..."
		}
		if len(answerPreview) > 30 {
			answerPreview = answerPreview[:30] + "..."
		}

		fmt.Printf("%d\t%s\t%s\t%s\t%.2f\n",
			id, question, answerPreview, status, confidence)
		count++
	}

	if count == 0 {
		fmt.Println("⚠️  没有找到任何学习知识")
	} else {
		fmt.Printf("✅ 找到 %d 个学习知识\n", count)
	}
}

func testSearch(db *sql.DB, searchTerm string) {
	// 测试修复后的搜索查询
	searchQuery := `
		SELECT id, question, answer, confidence
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		AND (question LIKE CONCAT('%', ?, '%') OR answer LIKE CONCAT('%', ?, '%'))
		ORDER BY confidence DESC, created_at DESC
		LIMIT 5
	`
	
	rows, err := db.Query(searchQuery, searchTerm, searchTerm)
	if err != nil {
		fmt.Printf("❌ 搜索失败: %v\n", err)
		return
	}
	defer rows.Close()

	found := false
	for rows.Next() {
		var id int
		var question, answer string
		var confidence float32
		
		err := rows.Scan(&id, &question, &answer, &confidence)
		if err != nil {
			continue
		}
		
		if !found {
			fmt.Println("找到的结果:")
			found = true
		}
		
		// 截断长文本
		if len(question) > 40 {
			question = question[:40] + "..."
		}
		if len(answer) > 60 {
			answer = answer[:60] + "..."
		}
		
		fmt.Printf("  ID %d (置信度 %.2f): %s -> %s\n", id, confidence, question, answer)
	}
	
	if !found {
		fmt.Println("❌ 未找到匹配结果")
		
		// 尝试模糊搜索
		fmt.Println("🔍 尝试模糊搜索...")
		fuzzySearch(db, searchTerm)
	}
}

func fuzzySearch(db *sql.DB, searchTerm string) {
	// 获取所有知识进行应用层过滤
	query := `
		SELECT id, question, answer, confidence
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT 50
	`
	
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 模糊搜索失败: %v\n", err)
		return
	}
	defer rows.Close()

	searchLower := strings.ToLower(searchTerm)
	found := false
	
	for rows.Next() {
		var id int
		var question, answer string
		var confidence float32
		
		err := rows.Scan(&id, &question, &answer, &confidence)
		if err != nil {
			continue
		}
		
		questionLower := strings.ToLower(question)
		answerLower := strings.ToLower(answer)
		
		if strings.Contains(questionLower, searchLower) || strings.Contains(answerLower, searchLower) {
			if !found {
				fmt.Println("模糊搜索找到的结果:")
				found = true
			}
			
			// 截断长文本
			if len(question) > 40 {
				question = question[:40] + "..."
			}
			if len(answer) > 60 {
				answer = answer[:60] + "..."
			}
			
			fmt.Printf("  ID %d (置信度 %.2f): %s -> %s\n", id, confidence, question, answer)
		}
	}
	
	if !found {
		fmt.Println("❌ 模糊搜索也未找到匹配结果")
	}
}

func addTestKnowledge(db *sql.DB) {
	// 添加一个包含"一言百宝箱"的测试知识
	insertQuery := `
		INSERT INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	question := "什么是一言百宝箱？"
	answer := "一言百宝箱是一个提供随机名言警句的在线服务，用户可以获取各种励志、哲理、情感等类型的句子。"
	source := "manual_test"
	confidence := 0.9
	category := "test"
	keywords := `["一言百宝箱", "名言", "警句", "随机"]`
	context := "测试数据"
	learnedFrom := "test_user"
	status := "approved"
	
	_, err := db.Exec(insertQuery, question, answer, source, confidence, category, 
		keywords, context, learnedFrom, status)
	if err != nil {
		fmt.Printf("❌ 添加测试知识失败: %v\n", err)
	} else {
		fmt.Printf("✅ 添加测试知识成功: %s\n", question)
	}
}
