package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🕷️ 快速检查爬虫状态...")

	// 检查爬取目标
	fmt.Println("\n📋 爬取目标:")
	rows, err := db.Query("SELECT id, name, url, keywords FROM crawl_targets ORDER BY id")
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var name, url, keywords string
		rows.Scan(&id, &name, &url, &keywords)
		fmt.Printf("  %d: %s (%s) - %s\n", id, name, url, keywords)
	}

	// 检查爬取结果
	fmt.Println("\n📊 爬取结果:")
	var count int
	db.QueryRow("SELECT COUNT(*) FROM crawl_results").Scan(&count)
	fmt.Printf("  总数: %d\n", count)

	if count > 0 {
		fmt.Println("  最近结果:")
		rows2, err := db.Query(`
			SELECT r.id, r.target_id, r.title, r.status, r.crawled_at, LENGTH(r.content) as content_len
			FROM crawl_results r 
			ORDER BY r.crawled_at DESC 
			LIMIT 5
		`)
		if err != nil {
			fmt.Printf("❌ 查询结果失败: %v\n", err)
			return
		}
		defer rows2.Close()

		for rows2.Next() {
			var id, targetID, contentLen int
			var title, status, crawledAt string
			rows2.Scan(&id, &targetID, &title, &status, &crawledAt, &contentLen)
			fmt.Printf("    %d: 目标%d - %s (%s) - %d字符 - %s\n", 
				id, targetID, title, status, contentLen, crawledAt)
		}
	}

	// 检查学习知识
	fmt.Println("\n📚 学习知识:")
	var learnedCount int
	db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&learnedCount)
	fmt.Printf("  总数: %d\n", learnedCount)

	if learnedCount > 0 {
		fmt.Println("  最近学习:")
		rows3, err := db.Query(`
			SELECT id, question, answer, source, confidence, status, created_at
			FROM learned_knowledge 
			ORDER BY created_at DESC 
			LIMIT 5
		`)
		if err != nil {
			fmt.Printf("❌ 查询学习知识失败: %v\n", err)
			return
		}
		defer rows3.Close()

		for rows3.Next() {
			var id int
			var question, answer, source, status, createdAt string
			var confidence float64
			rows3.Scan(&id, &question, &answer, &source, &confidence, &status, &createdAt)
			
			// 截断长文本
			if len(question) > 30 {
				question = question[:30] + "..."
			}
			if len(answer) > 50 {
				answer = answer[:50] + "..."
			}
			
			fmt.Printf("    %d: %s -> %s (来源:%s, 置信度:%.2f, 状态:%s) - %s\n", 
				id, question, answer, source, confidence, status, createdAt)
		}
	}
}
