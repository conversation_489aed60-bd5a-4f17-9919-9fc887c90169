package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:park%25123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 测试搜索修复效果...")

	// 测试应用层搜索
	fmt.Println("\n🔍 测试应用层搜索...")
	testApplicationSearch(db, "一言百宝箱")
}

func testApplicationSearch(db *sql.DB, searchTerm string) {
	fmt.Printf("\n搜索词: '%s'\n", searchTerm)

	// 获取所有知识进行应用层过滤
	query := `
		SELECT id, question, answer, confidence
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT 50
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	searchLower := strings.ToLower(searchTerm)
	found := false
	matchCount := 0

	for rows.Next() {
		var id int
		var question, answer string
		var confidence float32

		err := rows.Scan(&id, &question, &answer, &confidence)
		if err != nil {
			continue
		}

		questionLower := strings.ToLower(question)
		answerLower := strings.ToLower(answer)

		if strings.Contains(questionLower, searchLower) || strings.Contains(answerLower, searchLower) {
			if !found {
				fmt.Println("找到的结果:")
				found = true
			}

			matchCount++

			// 截断长文本
			if len(question) > 40 {
				question = question[:40] + "..."
			}
			if len(answer) > 60 {
				answer = answer[:60] + "..."
			}

			fmt.Printf("  ID %d (置信度 %.2f): %s -> %s\n", id, confidence, question, answer)
		}
	}

	if !found {
		fmt.Println("❌ 未找到匹配结果")
	} else {
		fmt.Printf("✅ 找到 %d 个匹配结果\n", matchCount)
	}
}