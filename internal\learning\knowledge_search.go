package learning

import (
	"encoding/json"
	"math"
	"strings"
	"time"

	"faq-system/internal/logger"
)

// SearchLearnedKnowledge 搜索学习到的知识
func (kl *KnowledgeLearner) SearchLearnedKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🔍 开始搜索学习知识: %s", query)

	// 首先尝试向量搜索
	if kl.embedClient != nil {
		logger.Infof("🔍 尝试向量搜索...")
		vectorResults, err := kl.vectorSearchKnowledge(query, limit)
		if err != nil {
			logger.Warnf("向量搜索失败: %v", err)
		} else if len(vectorResults) > 0 {
			logger.Infof("✅ 向量搜索找到 %d 个结果", len(vectorResults))
			return vectorResults, nil
		} else {
			logger.Infof("向量搜索未找到结果")
		}
	} else {
		logger.Infof("向量搜索客户端不可用，使用文本搜索")
	}

	// 回退到文本搜索
	logger.Infof("🔍 尝试文本搜索...")
	results, err := kl.textSearchKnowledge(query, limit)
	if err != nil {
		logger.Errorf("文本搜索失败: %v", err)
		return nil, err
	}
	logger.Infof("✅ 文本搜索找到 %d 个结果", len(results))
	return results, nil
}

// vectorSearchKnowledge 向量搜索学习知识
func (kl *KnowledgeLearner) vectorSearchKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	// 生成查询向量
	queryVector, err := kl.embedClient.EmbedText(query)
	if err != nil {
		return nil, err
	}

	// 获取所有知识向量并计算相似度
	vectorQuery := `
		SELECT lk.id, lk.question, lk.answer, lk.source, lk.confidence, 
		       lk.category, lk.keywords, lk.context, lk.learned_from, 
		       lk.status, lk.created_at, kv.vector_data
		FROM learned_knowledge lk
		JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE lk.status IN ('approved', 'pending')
		ORDER BY lk.confidence DESC
	`

	rows, err := kl.db.Query(vectorQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	type knowledgeWithVector struct {
		knowledge  *LearnedKnowledge
		vector     []float32
		similarity float32
	}

	var candidates []knowledgeWithVector

	for rows.Next() {
		var knowledge LearnedKnowledge
		var vectorJSON, keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt, &vectorJSON)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)

		// 解析向量
		var vector []float32
		if err := json.Unmarshal([]byte(vectorJSON), &vector); err != nil {
			continue
		}

		// 计算相似度
		similarity := cosineSimilarity(queryVector, vector)

		candidates = append(candidates, knowledgeWithVector{
			knowledge:  &knowledge,
			vector:     vector,
			similarity: similarity,
		})
	}

	// 按相似度排序
	for i := 0; i < len(candidates)-1; i++ {
		for j := i + 1; j < len(candidates); j++ {
			if candidates[i].similarity < candidates[j].similarity {
				candidates[i], candidates[j] = candidates[j], candidates[i]
			}
		}
	}

	// 返回前N个结果
	if limit > len(candidates) {
		limit = len(candidates)
	}

	results := make([]*LearnedKnowledge, limit)
	for i := 0; i < limit; i++ {
		results[i] = candidates[i].knowledge
	}

	return results, nil
}

// textSearchKnowledge 文本搜索学习知识
func (kl *KnowledgeLearner) textSearchKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	// 先检查数据库中是否有学习知识
	var totalCount int
	countQuery := "SELECT COUNT(*) FROM learned_knowledge WHERE status IN ('approved', 'pending')"
	err := kl.db.QueryRow(countQuery).Scan(&totalCount)
	if err != nil {
		logger.Errorf("检查学习知识总数失败: %v", err)
	} else {
		logger.Infof("📊 数据库中共有 %d 条可用学习知识", totalCount)
	}

	// 使用应用层搜索避免数据库字符集问题
	logger.Infof("🔍 使用应用层搜索避免字符集问题")
	return kl.fallbackTextSearch(query, limit)

}

// cosineSimilarity 计算余弦相似度
func cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// ApproveKnowledge 批准学习到的知识
func (kl *KnowledgeLearner) ApproveKnowledge(knowledgeID int) error {
	query := `UPDATE learned_knowledge SET status = 'approved' WHERE id = ?`
	_, err := kl.db.Exec(query, knowledgeID)
	if err != nil {
		return err
	}

	logger.Infof("✅ 知识 %d 已批准", knowledgeID)
	return nil
}

// GetPendingKnowledge 获取待审核的知识
func (kl *KnowledgeLearner) GetPendingKnowledge(limit int) ([]*LearnedKnowledge, error) {
	query := `
		SELECT id, question, answer, source, confidence, category, 
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status = 'pending'
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		results = append(results, &knowledge)
	}

	return results, nil
}

// RecordKnowledgeUsage 记录知识使用情况
func (kl *KnowledgeLearner) RecordKnowledgeUsage(knowledgeID int, queryID int64, userID string, matchScore float32, wasHelpful *bool) error {
	query := `
		INSERT INTO knowledge_usage 
		(knowledge_id, query_id, user_id, match_score, was_helpful)
		VALUES (?, ?, ?, ?, ?)
	`

	_, err := kl.db.Exec(query, knowledgeID, queryID, userID, matchScore, wasHelpful)
	if err != nil {
		return err
	}

	// 更新知识的使用统计
	updateQuery := `
		UPDATE learned_knowledge 
		SET usage_count = usage_count + 1,
		    last_used = NOW()
		WHERE id = ?
	`
	kl.db.Exec(updateQuery, knowledgeID)

	return nil
}

// GetKnowledgeStats 获取知识统计信息
func (kl *KnowledgeLearner) GetKnowledgeStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总知识数量
	var totalCount int
	kl.db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&totalCount)
	stats["total_knowledge"] = totalCount

	// 按状态分组
	statusQuery := `
		SELECT status, COUNT(*) as count 
		FROM learned_knowledge 
		GROUP BY status
	`
	rows, err := kl.db.Query(statusQuery)
	if err == nil {
		statusStats := make(map[string]int)
		for rows.Next() {
			var status string
			var count int
			rows.Scan(&status, &count)
			statusStats[status] = count
		}
		rows.Close()
		stats["by_status"] = statusStats
	}

	// 按分类分组
	categoryQuery := `
		SELECT category, COUNT(*) as count 
		FROM learned_knowledge 
		GROUP BY category
	`
	rows, err = kl.db.Query(categoryQuery)
	if err == nil {
		categoryStats := make(map[string]int)
		for rows.Next() {
			var category string
			var count int
			rows.Scan(&category, &count)
			categoryStats[category] = count
		}
		rows.Close()
		stats["by_category"] = categoryStats
	}

	// 最近学习的知识
	var recentCount int
	kl.db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)").Scan(&recentCount)
	stats["recent_learned"] = recentCount

	return stats, nil
}

// fallbackTextSearch 备用文本搜索方法，避免字符集问题
func (kl *KnowledgeLearner) fallbackTextSearch(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🔄 使用备用搜索方案")

	// 最简单的查询，不使用任何字符串匹配函数
	searchQuery := `
		SELECT id, question, answer, source, confidence, category,
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(searchQuery, limit*3) // 获取更多结果用于过滤
	if err != nil {
		logger.Errorf("备用搜索也失败: %v", err)
		return nil, err
	}
	defer rows.Close()

	var allResults []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		allResults = append(allResults, &knowledge)
	}

	// 在应用层进行文本匹配过滤
	var results []*LearnedKnowledge
	queryLower := strings.ToLower(query)

	for _, knowledge := range allResults {
		questionLower := strings.ToLower(knowledge.Question)
		answerLower := strings.ToLower(knowledge.Answer)

		if strings.Contains(questionLower, queryLower) || strings.Contains(answerLower, queryLower) {
			results = append(results, knowledge)
			if len(results) >= limit {
				break
			}
		}
	}

	logger.Infof("✅ 备用搜索找到 %d 个匹配结果", len(results))
	return results, nil
}
