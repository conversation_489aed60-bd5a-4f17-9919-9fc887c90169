package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 直接连接数据库
	db, err := sql.Open("mysql", "root:123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔧 修复爬虫目标配置...")

	// 1. 更新百度爬虫目标，改为爬取百度百科
	fmt.Println("1. 更新百度爬虫目标...")
	
	// 更新百度目标为百度百科的一个具体页面
	updateQuery := `
		UPDATE crawl_targets 
		SET name = '百度百科-人工智能', 
		    url = 'https://baike.baidu.com/item/人工智能/9180',
		    keywords = '["人工智能", "AI", "机器学习", "深度学习"]',
		    selectors = '{"title": ".lemmaWgt-lemmaTitle-title h1", "content": ".lemma-summary, .para"}',
		    category = 'technology'
		WHERE name = '百度爬虫' OR url LIKE '%baidu.com%'
	`
	
	result, err := db.Exec(updateQuery)
	if err != nil {
		fmt.Printf("❌ 更新百度目标失败: %v\n", err)
	} else {
		rowsAffected, _ := result.RowsAffected()
		fmt.Printf("✅ 更新了 %d 个百度目标\n", rowsAffected)
	}

	// 2. 添加一些更容易爬取的目标
	fmt.Println("2. 添加新的爬取目标...")
	
	targets := []struct {
		name      string
		url       string
		category  string
		keywords  []string
		selectors map[string]string
	}{
		{
			name:     "维基百科-机器学习",
			url:      "https://zh.wikipedia.org/wiki/机器学习",
			category: "technology",
			keywords: []string{"机器学习", "算法", "数据科学"},
			selectors: map[string]string{
				"title":   "h1.firstHeading",
				"content": ".mw-parser-output p, .mw-parser-output .mw-headline",
			},
		},
		{
			name:     "简书-技术文章",
			url:      "https://www.jianshu.com/p/1234567890ab",
			category: "technology",
			keywords: []string{"技术", "编程", "开发"},
			selectors: map[string]string{
				"title":   "h1._1RuRku",
				"content": "article .show-content",
			},
		},
		{
			name:     "CSDN-技术博客",
			url:      "https://blog.csdn.net/weixin_12345678/article/details/123456789",
			category: "technology", 
			keywords: []string{"技术", "博客", "编程"},
			selectors: map[string]string{
				"title":   ".title-article",
				"content": "#content_views",
			},
		},
	}

	for _, target := range targets {
		keywordsJSON, _ := json.Marshal(target.keywords)
		selectorsJSON, _ := json.Marshal(target.selectors)
		
		insertQuery := `
			INSERT IGNORE INTO crawl_targets 
			(name, url, type, category, keywords, selectors, schedule, enabled, created_at, updated_at)
			VALUES (?, ?, 'website', ?, ?, ?, '0 */2 * * *', true, NOW(), NOW())
		`
		
		_, err := db.Exec(insertQuery, target.name, target.url, target.category, 
			keywordsJSON, selectorsJSON)
		if err != nil {
			fmt.Printf("❌ 添加目标 %s 失败: %v\n", target.name, err)
		} else {
			fmt.Printf("✅ 添加目标: %s\n", target.name)
		}
	}

	// 3. 添加一个简单的测试页面
	fmt.Println("3. 添加测试页面...")
	
	testKeywords := []string{"测试", "示例", "内容"}
	testSelectors := map[string]string{
		"title":   "title",
		"content": "body",
	}
	
	testKeywordsJSON, _ := json.Marshal(testKeywords)
	testSelectorsJSON, _ := json.Marshal(testSelectors)
	
	testQuery := `
		INSERT IGNORE INTO crawl_targets 
		(name, url, type, category, keywords, selectors, schedule, enabled, created_at, updated_at)
		VALUES ('测试页面-Example.com', 'https://example.com', 'website', 'test', ?, ?, '0 */1 * * *', true, NOW(), NOW())
	`
	
	_, err = db.Exec(testQuery, testKeywordsJSON, testSelectorsJSON)
	if err != nil {
		fmt.Printf("❌ 添加测试页面失败: %v\n", err)
	} else {
		fmt.Printf("✅ 添加测试页面成功\n")
	}

	// 4. 显示当前所有目标
	fmt.Println("\n📋 当前爬取目标:")
	rows, err := db.Query("SELECT id, name, url, category, keywords FROM crawl_targets ORDER BY id")
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var name, url, category, keywords string
		rows.Scan(&id, &name, &url, &category, &keywords)
		
		// 截断长URL
		if len(url) > 50 {
			url = url[:50] + "..."
		}
		
		fmt.Printf("  %d: %s (%s) - %s - %s\n", id, name, category, url, keywords)
	}

	fmt.Println("\n🎉 爬虫目标配置修复完成！")
	fmt.Println("\n💡 建议:")
	fmt.Println("1. 重启FAQ系统以加载新配置")
	fmt.Println("2. 在爬虫仪表板中手动触发爬取")
	fmt.Println("3. 检查爬取结果和学习知识")
}
